/* 分摊表详情页面样式优化 */

/* 基本信息区域整体样式 */
.allocation-detail-container {
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.allocation-detail-header {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    color: white;
    padding: 8px 12px;
    margin: 0;
    border-bottom: 1px solid #388e3c;
}

.allocation-detail-header h5 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.allocation-detail-header h5 i {
    margin-right: 6px;
    font-size: 12px;
}

.allocation-detail-content {
    padding: 8px;
    background: #fafbfc;
}

/* 信息行样式 */
.info-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 6px;
    background: white;
    border-radius: 3px;
    padding: 6px 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    border-left: 2px solid #66bb6a;
}

.info-row:last-child {
    margin-bottom: 0;
}

/* 信息项样式 */
.info-item {
    flex: 1;
    min-width: 150px;
    margin-bottom: 4px;
    padding-right: 8px;
}

.info-item:last-child {
    padding-right: 0;
}

.info-item.full-width {
    flex: 1 1 100%;
    min-width: 100%;
}

.info-item.half-width {
    flex: 1 1 50%;
    min-width: 200px;
}

.info-item.third-width {
    flex: 1 1 33.333%;
    min-width: 140px;
}

.info-item.quarter-width {
    flex: 1 1 25%;
    min-width: 180px;
}

.info-item.fifth-width {
    flex: 1 1 20%;
    min-width: 140px;
}

/* 标签样式 */
.info-label {
    display: inline-block;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 2px;
    font-size: 12px;
    min-width: 60px;
    position: relative;
}

.info-label::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 18px;
    height: 1px;
    background: #66bb6a;
    border-radius: 1px;
}

/* 内容样式 */
.info-value {
    display: flex;
    align-items: center;
    color: #2d3748;
    font-size: 13px;
    line-height: 1.3;
    padding: 4px 6px;
    background: #f7fafc;
    border-radius: 3px;
    border: 1px solid #e2e8f0;
    min-height: 24px;
}

/* 状态徽章优化 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.2px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

.status-badge.status-pending {
    background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
    color: #2e7d32;
    border: 1px solid #81c784;
}

.status-badge.status-approved {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    color: #ffffff;
    border: 1px solid #4caf50;
}

.status-badge.status-rejected {
    background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
    color: #c62828;
    border: 1px solid #e57373;
}

/* 修改意见特殊样式 */
.comments-section {
    background: #fff8e1;
    border: 1px solid #ffcc02;
    border-left: 2px solid #ff9800;
    border-radius: 3px;
    padding: 6px 8px;
    margin-top: 6px;
}

.comments-section .info-label {
    color: #e65100;
    font-weight: 700;
    margin-bottom: 2px;
}

.comments-section .info-value {
    background: #ffffff;
    border: 1px solid #ffcc02;
    color: #bf360c;
    font-style: italic;
    min-height: auto;
    padding: 4px 6px;
    line-height: 1.4;
}

/* 时间信息特殊样式 */
.time-info .info-value {
    background: #e8f5e8;
    border-color: #66bb6a;
    color: #2e7d32;
    font-family: 'Courier New', monospace;
}

/* 人员信息样式 */
.person-info .info-value {
    background: #f1f8e9;
    border-color: #81c784;
    color: #388e3c;
}

/* 基本信息样式 */
.basic-info .info-value {
    background: #e8f5e8;
    border-color: #66bb6a;
    color: #2e7d32;
}

/* 人员时间信息样式 */
.person-time-info .info-value {
    background: #f1f8e9;
    border-color: #81c784;
    color: #388e3c;
}

/* 导出按钮区域 */
.export-button-area {
    display: flex;
    justify-content: flex-end;
    padding-top: 8px;
    margin-top: 6px;
    border-top: 1px solid #e8f5e8;
}

.btn-export {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(76, 175, 80, 0.3);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
}

.btn-export:hover {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.4);
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

.btn-export i {
    margin-right: 4px;
    font-size: 11px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .allocation-detail-content {
        padding: 6px;
    }

    .info-row {
        padding: 4px 6px;
        margin-bottom: 4px;
    }

    .info-item {
        flex: 1 1 100%;
        min-width: 100%;
        padding-right: 0;
        margin-bottom: 3px;
    }

    .info-item.half-width,
    .info-item.third-width,
    .info-item.quarter-width,
    .info-item.fifth-width {
        flex: 1 1 100%;
        min-width: 100%;
    }

    .allocation-detail-header {
        padding: 6px 10px;
    }

    .allocation-detail-header h5 {
        font-size: 13px;
    }
}

@media (max-width: 576px) {
    .allocation-detail-header h5 {
        font-size: 14px;
    }
    
    .info-label {
        font-size: 13px;
    }
    
    .info-value {
        font-size: 14px;
        padding: 6px 10px;
    }
    
    .status-badge {
        padding: 6px 12px;
        font-size: 12px;
    }
}

/* 动画效果 */
.info-row {
    transition: all 0.3s ease;
}

.info-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.info-value {
    transition: all 0.2s ease;
}

.info-value:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

/* 图标样式 */
.info-icon {
    margin-right: 4px;
    color: #66bb6a;
    font-size: 11px;
}

/* 分摊表详细内容美化样式 */
.ibox {
    background: #ffffff;
    border: 2px solid #e3f2fd;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.ibox:hover {
    border-color: #2196f3;
    box-shadow: 0 4px 20px rgba(33, 150, 243, 0.15);
    transform: translateY(-2px);
}

.ibox-title {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    padding: 15px 20px;
    margin: 0;
    border-bottom: 3px solid #1565c0;
    position: relative;
}

.ibox-title::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #64b5f6, #42a5f5, #2196f3, #1e88e5, #1976d2);
}

.ibox-title h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.ibox-title h5::before {
    content: '\f0ce';
    font-family: 'FontAwesome';
    margin-right: 10px;
    font-size: 14px;
}

.ibox-tools {
    display: flex;
    align-items: center;
}

.ibox-tools .btn {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ibox-tools .btn:hover {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
    transform: translateY(-2px);
}

.ibox-tools .btn i {
    margin-right: 6px;
    font-size: 12px;
}

.ibox-content {
    padding: 20px;
    background: #fafbfc;
    position: relative;
}

.ibox-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background: linear-gradient(90deg, transparent, #e3f2fd, transparent);
}

/* 表格美化 */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e3f2fd;
}

.table {
    margin-bottom: 0;
    background: white;
}

.table thead th {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    font-weight: 600;
    border: none;
    padding: 15px 12px;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
    position: relative;
    border-bottom: 2px solid #1565c0;
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, #64b5f6, #2196f3, #1976d2);
}

.table tbody td {
    padding: 12px;
    border: 1px solid #e3f2fd;
    vertical-align: middle;
    font-size: 13px;
    transition: all 0.2s ease;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #f3f9ff 0%, #e8f4fd 100%);
    transform: scale(1.01);
}

.table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, #f0f7ff 0%, #e3f2fd 100%);
}

.table-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    font-weight: 600;
    color: #1565c0;
    border: 2px solid #2196f3 !important;
}

.table-info td {
    border-color: #2196f3 !important;
    font-size: 14px;
    padding: 15px 12px;
}

/* 加载状态美化 */
.text-center {
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.text-muted {
    color: #9e9e9e !important;
}

.text-danger {
    color: #f44336 !important;
    font-weight: 600;
}
