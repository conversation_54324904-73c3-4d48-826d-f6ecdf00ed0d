package com.ruoyi.expense.service.impl;

import java.util.List;
import java.util.Map;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.expense.mapper.ExpenseDetailsMapper;
import com.ruoyi.expense.domain.ExpenseDetails;
import com.ruoyi.expense.domain.ExpenseBill;
import com.ruoyi.expense.service.IExpenseDetailsService;
import com.ruoyi.expense.service.IExpenseBillService;
import com.ruoyi.common.core.text.Convert;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

// 导入导出相关包
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import java.io.IOException;
import java.io.File;
import java.io.FileOutputStream;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.file.FileUtils;
import org.springframework.http.MediaType;
import javax.servlet.http.HttpServletResponse;

/**
 * 费用明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-13
 */
@Service
public class ExpenseDetailsServiceImpl implements IExpenseDetailsService 
{
    private static final Logger log = LoggerFactory.getLogger(ExpenseDetailsServiceImpl.class);
    
    @Autowired
    private ExpenseDetailsMapper expenseDetailsMapper;
    
    @Autowired
    private IExpenseBillService expenseBillService;

    /**
     * 查询费用明细
     * 
     * @param id 费用明细ID
     * @return 费用明细
     */
    @Override
    public ExpenseDetails selectExpenseDetailsById(Long id)
    {
        return expenseDetailsMapper.selectExpenseDetailsById(id);
    }

    /**
     * 查询费用明细列表
     * 
     * @param expenseDetails 费用明细
     * @return 费用明细
     */
    @Override
    public List<ExpenseDetails> selectExpenseDetailsList(ExpenseDetails expenseDetails)
    {
        return expenseDetailsMapper.selectExpenseDetailsList(expenseDetails);
    }

    /**
     * 新增费用明细
     * 
     * @param expenseDetails 费用明细
     * @return 结果
     */
    @Override
    public int insertExpenseDetails(ExpenseDetails expenseDetails)
    {
        return expenseDetailsMapper.insertExpenseDetails(expenseDetails);
    }

    /**
     * 修改费用明细
     * 
     * @param expenseDetails 费用明细
     * @return 结果
     */
    @Override
    public int updateExpenseDetails(ExpenseDetails expenseDetails)
    {
        return expenseDetailsMapper.updateExpenseDetails(expenseDetails);
    }

    /**
     * 删除费用明细对象
     * 
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteExpenseDetailsByIds(String ids)
    {
        return expenseDetailsMapper.deleteExpenseDetailsByIds(Convert.toLongArray(ids));
    }

    /**
     * 删除费用明细信息
     * 
     * @param id 费用明细ID
     * @return 结果
     */
    @Override
    public int deleteExpenseDetailsById(Long id)
    {
        return expenseDetailsMapper.deleteExpenseDetailsById(id);
    }
    
    /**
     * 格式化价格值，限制小数位数为2位并使用四舍五入
     * 
     * @param value BigDecimal价格值
     * @return 格式化后的价格字符串，最多保留2位小数
     */
    private String formatPriceValue(BigDecimal value) {
        if (value == null) {
            return "0.00";
        }
        return value.setScale(2, RoundingMode.HALF_UP).toString();
    }
    
    /**
     * 格式化价格值为双精度浮点数，限制小数位数为2位
     * 
     * @param value BigDecimal价格值
     * @return 格式化后的价格双精度浮点数，最多保留2位小数
     */
    private double formatPriceValueToDouble(BigDecimal value) {
        if (value == null) {
            return 0.00;
        }
        return value.setScale(2, RoundingMode.HALF_UP).doubleValue();
    }
    
    /**
     * 导出账单关联的费用明细
     *
     * @param billId 账单ID
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    @Override
    public void exportBillDetails(Long billId, HttpServletResponse response) throws Exception
    {
        // 查询账单
        ExpenseBill bill = expenseBillService.selectExpenseBillById(billId);
        if (bill == null) {
            writeErrorResponse(response, "账单不存在");
            return;
        }
        
        // 查询费用明细
        ExpenseDetails query = new ExpenseDetails();
        query.setExpenseType(bill.getExpense_type());
        query.setBillingCycle(bill.getBilling_cycle());
        query.setTransferDepartment(bill.getTransfer_department());
        List<ExpenseDetails> detailsList = selectExpenseDetailsList(query);
        
        if (detailsList == null || detailsList.isEmpty()) {
            writeErrorResponse(response, "该账单没有关联的费用明细数据");
            return;
        }
        
        // 生成Excel文件
        generateDetailsExcel(bill, detailsList, response);
    }

    /**
     * 导出部门账单明细
     *
     * @param transferDepartment 划账部门
     * @param expenseType 费用类型
     * @param billingCycle 计费周期
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    @Override
    public void exportDepartmentDetails(String transferDepartment, String expenseType, String billingCycle, HttpServletResponse response) throws Exception
    {
        // 查询费用明细
        ExpenseDetails query = new ExpenseDetails();
        query.setExpenseType(expenseType);
        query.setBillingCycle(billingCycle);
        query.setTransferDepartment(transferDepartment);
        List<ExpenseDetails> detailsList = selectExpenseDetailsList(query);

        if (detailsList == null || detailsList.isEmpty()) {
            writeErrorResponse(response, "该部门没有相关的费用明细数据");
            return;
        }

        // 创建一个虚拟的账单对象用于导出
        ExpenseBill virtualBill = new ExpenseBill();
        virtualBill.setExpense_type(expenseType);
        virtualBill.setBilling_cycle(billingCycle);
        virtualBill.setTransfer_department(transferDepartment);

        // 生成Excel文件
        generateDetailsExcel(virtualBill, detailsList, response);
    }
    
    /**
     * 生成费用明细Excel文件
     *
     * @param bill 账单信息
     * @param detailsList 费用明细列表
     * @param response HTTP响应对象
     * @throws Exception 处理过程中的异常
     */
    private void generateDetailsExcel(ExpenseBill bill, List<ExpenseDetails> detailsList, HttpServletResponse response) throws Exception
    {
        // 构建文件名：[费用类型]-[计费周期]-[划账部门]-费用明细
        String fileName = bill.getExpense_type() + "-" + bill.getBilling_cycle() + "-" + bill.getTransfer_department() + "-费用明细";
        
        // 创建工作簿
        Workbook wb = new SXSSFWorkbook(500);
        
        try {
            // 创建样式
            Map<String, CellStyle> styles = createExcelStyles(wb);
            
            // 创建工作表
            Sheet sheet = wb.createSheet("费用明细");
            
            // 设置列宽
            sheet.setColumnWidth(0, 6000);
            sheet.setColumnWidth(1, 4000);
            
            // 添加账单信息
            int rowNum = createBillInfoSection(sheet, bill, styles);
            
            // 添加明细表头和数据
            createDetailsSection(sheet, detailsList, styles, rowNum);
            
            // 保存文件并输出到响应
            outputExcelToResponse(wb, fileName, response);
            
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e) {
                    log.error("关闭工作簿异常{}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 创建Excel样式
     *
     * @param wb 工作簿
     * @return 样式映射
     */
    private Map<String, CellStyle> createExcelStyles(Workbook wb) {
        java.util.Map<String, CellStyle> styles = new java.util.HashMap<>();
        
        // 表头样式
        CellStyle headerStyle = wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font headerFont = wb.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        styles.put("header", headerStyle);
        
        // 信息样式
        CellStyle infoStyle = wb.createCellStyle();
        infoStyle.setAlignment(HorizontalAlignment.LEFT);
        Font infoFont = wb.createFont();
        infoFont.setFontHeightInPoints((short) 11);
        infoStyle.setFont(infoFont);
        styles.put("info", infoStyle);
        
        return styles;
    }
    
    /**
     * 创建账单信息部分
     *
     * @param sheet 工作表
     * @param bill 账单信息
     * @param styles 样式映射
     * @return 下一行的行号
     */
    private int createBillInfoSection(Sheet sheet, ExpenseBill bill, Map<String, CellStyle> styles) {
        int rowNum = 0;
        
        // 标题行
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("账单信息");
        titleCell.setCellStyle(styles.get("header"));
        
        // 账单信息行
        Row infoRow1 = sheet.createRow(rowNum++);
        Cell cell1 = infoRow1.createCell(0);
        cell1.setCellValue("费用类型：" + bill.getExpense_type());
        cell1.setCellStyle(styles.get("info"));
        
        Row infoRow2 = sheet.createRow(rowNum++);
        Cell cell2 = infoRow2.createCell(0);
        cell2.setCellValue("计费周期：" + bill.getBilling_cycle());
        cell2.setCellStyle(styles.get("info"));
        
        Row infoRow3 = sheet.createRow(rowNum++);
        Cell cell3 = infoRow3.createCell(0);
        cell3.setCellValue("划账部门：" + bill.getTransfer_department());
        cell3.setCellStyle(styles.get("info"));
        
        Row infoRow4 = sheet.createRow(rowNum++);
        Cell cell4 = infoRow4.createCell(0);
        cell4.setCellValue("账单状态：" + formatBillStatus(bill.getStatus()));
        cell4.setCellStyle(styles.get("info"));
        
        Row infoRow5 = sheet.createRow(rowNum++);
        Cell cell5 = infoRow5.createCell(0);
        cell5.setCellValue("含税总价：" + formatPriceValue(bill.getTotal_price_with_tax()));
        cell5.setCellStyle(styles.get("info"));
        
        Row infoRow6 = sheet.createRow(rowNum++);
        Cell cell6 = infoRow6.createCell(0);
        cell6.setCellValue("不含税总价：" + formatPriceValue(bill.getTotal_price_without_tax()));
        cell6.setCellStyle(styles.get("info"));
        
        // 空行
        rowNum++;
        
        return rowNum;
    }
    
    /**
     * 创建明细数据部分
     *
     * @param sheet 工作表
     * @param detailsList 费用明细列表
     * @param styles 样式映射
     * @param rowNum 起始行号
     */
    private void createDetailsSection(Sheet sheet, List<ExpenseDetails> detailsList, Map<String, CellStyle> styles, int rowNum) {
        // 添加明细表头
        Row headerRow = sheet.createRow(rowNum++);
        String[] headers = {
            "名称", "编号", "品牌", "具体规格", "费用类型", "计费周期", "划账部门", 
            "数量", "费用变动情况", "备注", "含税单价", "不含税单价", "含税单行总价", "不含税单行总价"
        };
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.get("header"));
            // 自动调整列宽
            sheet.setColumnWidth(i, 15 * 256);
        }
        
        // 添加明细数据
        for (int i = 0; i < detailsList.size(); i++) {
            ExpenseDetails detail = detailsList.get(i);
            Row dataRow = sheet.createRow(rowNum++);
            
            dataRow.createCell(0).setCellValue(detail.getName() != null ? detail.getName() : "");
            dataRow.createCell(1).setCellValue(detail.getNumber() != null ? detail.getNumber() : "");
            dataRow.createCell(2).setCellValue(detail.getBrand() != null ? detail.getBrand() : "");
            dataRow.createCell(3).setCellValue(detail.getSpecificSpecification() != null ? detail.getSpecificSpecification() : "");
            dataRow.createCell(4).setCellValue(detail.getExpenseType() != null ? detail.getExpenseType() : "");
            dataRow.createCell(5).setCellValue(detail.getBillingCycle() != null ? detail.getBillingCycle() : "");
            dataRow.createCell(6).setCellValue(detail.getTransferDepartment() != null ? detail.getTransferDepartment() : "");
            dataRow.createCell(7).setCellValue(detail.getQuantity() != null ? detail.getQuantity() : 0);
            dataRow.createCell(8).setCellValue(detail.getExpenseChangeStatus() != null ? detail.getExpenseChangeStatus() : "");
            dataRow.createCell(9).setCellValue(detail.getRemarks() != null ? detail.getRemarks() : "");
            dataRow.createCell(10).setCellValue(formatPriceValueToDouble(detail.getUnitPriceIncludingTax()));
            dataRow.createCell(11).setCellValue(formatPriceValueToDouble(detail.getUnitPriceExcludingTax()));
            dataRow.createCell(12).setCellValue(formatPriceValueToDouble(detail.getTotalLinePriceIncludingTax()));
            dataRow.createCell(13).setCellValue(formatPriceValueToDouble(detail.getTotalLinePriceExcludingTax()));
        }
    }
    
    /**
     * 输出Excel到HTTP响应
     *
     * @param wb 工作簿
     * @param fileName 文件名
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void outputExcelToResponse(Workbook wb, String fileName, HttpServletResponse response) throws IOException {
        String filename = encodingFilename(fileName);
        FileOutputStream out = null;
        
        try {
            String filePath = getAbsoluteFile(filename);
            out = new FileOutputStream(filePath);
            wb.write(out);
            
            // 返回自定义响应，不使用通用下载处理
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, fileName + ".xlsx");
            FileUtils.writeBytes(filePath, response.getOutputStream());
            FileUtils.deleteFile(filePath);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("关闭输出流异常{}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 编码文件名
     */
    private String encodingFilename(String filename) {
        return filename + ".xlsx";
    }
    
    /**
     * 获取下载路径
     */
    private String getAbsoluteFile(String filename) {
        String downloadPath = RuoYiConfig.getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }
    
    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        response.getWriter().print("{\"code\":500,\"msg\":\"" + message + "\"}");
    }
    
    /**
     * 格式化账单状态
     */
    private String formatBillStatus(String value) {
        switch(value) {
            case "0": return "已入库";
            case "1": return "已发布";
            case "2": return "已确认";
            case "3": return "已退回";
            default: return value;
        }
    }
}
