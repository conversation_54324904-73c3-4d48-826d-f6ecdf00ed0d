<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('分摊表详情')" />
    <link th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css}" rel="stylesheet"/>
    <link th:href="@{/css/allocation_table_detail.css}" rel="stylesheet"/>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="row">
            <div class="col-sm-12">
                <div class="allocation-detail-container">
                    <div class="allocation-detail-header">
                        <h5><i class="fa fa-info-circle"></i>分摊表基本信息</h5>
                    </div>
                    <div class="allocation-detail-content">
                        <!-- 基本信息行（合并前两行） -->
                        <div class="info-row basic-info">
                            <div class="info-item quarter-width">
                                <span class="info-label"><i class="fa fa-file-text info-icon"></i>分摊表名称</span>
                                <span class="info-value" th:text="${allocationTable.tableName}"></span>
                            </div>
                            <div class="info-item quarter-width">
                                <span class="info-label"><i class="fa fa-flag info-icon"></i>状态</span>
                                <span class="info-value">
                                    <span th:if="${allocationTable.status == '审核中'}" class="status-badge status-pending">审核中</span>
                                    <span th:if="${allocationTable.status == '已审核'}" class="status-badge status-approved">已审核</span>
                                    <span th:if="${allocationTable.status == '已拒绝'}" class="status-badge status-rejected">已拒绝</span>
                                </span>
                            </div>
                            <div class="info-item quarter-width">
                                <span class="info-label"><i class="fa fa-tags info-icon"></i>费用类型</span>
                                <span class="info-value" th:text="${allocationTable.expenseType}"></span>
                            </div>
                            <div class="info-item quarter-width">
                                <span class="info-label"><i class="fa fa-calendar info-icon"></i>计费周期</span>
                                <span class="info-value" th:text="${allocationTable.billingCycle}"></span>
                            </div>
                        </div>

                        <!-- 人员和时间信息行（合并第二行和第三行） -->
                        <div class="info-row person-time-info">
                            <div class="info-item fifth-width">
                                <span class="info-label"><i class="fa fa-user info-icon"></i>制表人</span>
                                <span class="info-value" th:text="${allocationTable.preparer}"></span>
                            </div>
                            <div class="info-item fifth-width">
                                <span class="info-label"><i class="fa fa-user-check info-icon"></i>复核人</span>
                                <span class="info-value" th:text="${allocationTable.reviewer}"></span>
                            </div>
                            <div class="info-item fifth-width">
                                <span class="info-label"><i class="fa fa-user-cog info-icon"></i>负责人</span>
                                <span class="info-value" th:text="${allocationTable.responsiblePerson ?: ''}"></span>
                            </div>
                            <div class="info-item fifth-width">
                                <span class="info-label"><i class="fa fa-clock-o info-icon"></i>创建时间</span>
                                <span class="info-value" th:text="${#dates.format(allocationTable.createTime, 'yyyy-MM-dd HH:mm:ss')}"></span>
                            </div>
                            <div class="info-item fifth-width">
                                <span class="info-label"><i class="fa fa-check-circle info-icon"></i>确认时间</span>
                                <span class="info-value" th:text="${allocationTable.confirmTime != null ? #dates.format(allocationTable.confirmTime, 'yyyy-MM-dd HH:mm:ss') : '未确认'}"></span>
                            </div>
                        </div>

                        <!-- 修改意见（条件显示） -->
                        <div class="comments-section" th:if="${allocationTable.comments != null && allocationTable.comments != ''}">
                            <div class="info-item full-width">
                                <span class="info-label"><i class="fa fa-comment info-icon"></i>修改意见</span>
                                <span class="info-value" th:text="${allocationTable.comments}"></span>
                            </div>
                        </div>

                        <!-- 导出按钮区域 -->
                        <div class="export-button-area">
                            <a class="btn btn-export btn-sm" id="exportBtn">
                                <i class="fa fa-download"></i> 导出文件
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-sm-12">
                <div class="ibox">
                    <div class="ibox-title">
                        <h5>分摊表详细内容</h5>
                    </div>
                    <div class="ibox-content">
                        <div id="allocationContent">
                            <p class="text-center">正在加载分摊表数据...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        /*<![CDATA[*/
        var allocationTable = /*[[${allocationTable}]]*/ {};
        var ctx = /*[[${#httpServletRequest.getContextPath()}]]*/ '';
        /*]]>*/

        $(function() {
            // 加载分摊表详细内容
            loadAllocationContent();
            
            // 导出按钮点击事件
            $("#exportBtn").click(function() {
                window.location.href = ctx + "/expense/allocation_table/exportDetail/" + allocationTable.id;
            });
        });

        function loadAllocationContent() {
            // 请求分摊表数据
            $.post(ctx + "/expense/allocation_table/getAllocationData", {
                expenseType: allocationTable.expenseType,
                billingCycle: allocationTable.billingCycle
            }, function(result) {
                if (result.code == 0 && result.data && result.data.length > 0) {
                    // 创建分摊表展示
                    createAllocationTable(result.data);
                } else {
                    $("#allocationContent").html('<p class="text-center text-muted">暂无相关账单数据</p>');
                }
            }).fail(function() {
                $("#allocationContent").html('<p class="text-center text-danger">加载分摊表数据失败</p>');
            });
        }

        function createAllocationTable(bills) {
            // 创建分摊表展示
            var tableHtml = '<div class="table-responsive">';
            tableHtml += '<table class="table table-bordered table-striped">';
            tableHtml += '<thead>';
            tableHtml += '<tr>';
            tableHtml += '<th>序号</th>';
            tableHtml += '<th>管辖行/部门</th>';
            tableHtml += '<th>分摊内容</th>';
            tableHtml += '<th>入账核算码</th>';
            tableHtml += '<th>金额(不含税,单位:元)</th>';
            tableHtml += '<th>操作</th>';
            tableHtml += '</tr>';
            tableHtml += '</thead>';
            tableHtml += '<tbody id="allocationTableBody">';
            tableHtml += '</tbody>';
            tableHtml += '</table>';
            tableHtml += '</div>';
            
            $("#allocationContent").html(tableHtml);
            
            // 填充分摊表数据
            fillAllocationTableData(bills);
        }

        function fillAllocationTableData(bills) {
            // 获取入账核算码字典映射
            var expenseCodeMap = {};
            
            // 先获取字典数据
            $.post(ctx + "/expense/bill_manage/getDictData", {
                dictType: "expense-code"
            }, function(result) {
                if (result.code === 0 && result.data) {
                    $.each(result.data, function(index, item) {
                        // 根据费用类型（dictValue）获取入账核算码（dictLabel）
                        expenseCodeMap[item.dictValue] = item.dictLabel;
                    });
                }
                
                // 填充表格数据
                var tbody = '';
                var totalAmount = 0;
                
                $.each(bills, function(i, bill) {
                    var serialNum = i + 1;
                    var transferDepartment = bill.transfer_department || '';
                    var expenseType = bill.expense_type || '';
                    var expenseCode = expenseCodeMap[expenseType] || '6576'; // 默认值
                    var amount = bill.total_price_without_tax || 0;
                    
                    if (amount) {
                        totalAmount += parseFloat(amount);
                    }
                    
                    tbody += '<tr>';
                    tbody += '<td>' + serialNum + '</td>';
                    tbody += '<td>' + transferDepartment + '</td>';
                    tbody += '<td>' + expenseType + '</td>';
                    tbody += '<td>' + expenseCode + '</td>';
                    tbody += '<td>' + formatAmount(amount) + '</td>';
                    tbody += '<td>';
                    tbody += '<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDepartmentBillDetail(\'' + transferDepartment + '\', \'' + expenseType + '\', \'' + allocationTable.billingCycle + '\')"><i class="fa fa-eye"></i>在线查看明细</a> ';
                    tbody += '<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="exportDepartmentBillDetail(\'' + transferDepartment + '\', \'' + expenseType + '\', \'' + allocationTable.billingCycle + '\')"><i class="fa fa-download"></i>导出明细</a>';
                    tbody += '</td>';
                    tbody += '</tr>';
                });
                
                // 添加合计行
                tbody += '<tr class="table-info">';
                tbody += '<td colspan="4" class="text-center"><strong>合计</strong></td>';
                tbody += '<td><strong>' + formatAmount(totalAmount) + '</strong></td>';
                tbody += '<td></td>'; // 操作列留空
                tbody += '</tr>';
                
                $("#allocationTableBody").html(tbody);
            }).fail(function() {
                // 如果获取字典失败，使用默认值
                var tbody = '';
                var totalAmount = 0;
                
                $.each(bills, function(i, bill) {
                    var serialNum = i + 1;
                    var transferDepartment = bill.transfer_department || '';
                    var expenseType = bill.expense_type || '';
                    var amount = bill.total_price_without_tax || 0;
                    
                    if (amount) {
                        totalAmount += parseFloat(amount);
                    }
                    
                    tbody += '<tr>';
                    tbody += '<td>' + serialNum + '</td>';
                    tbody += '<td>' + transferDepartment + '</td>';
                    tbody += '<td>' + expenseType + '</td>';
                    tbody += '<td>6576</td>'; // 默认入账核算码
                    tbody += '<td>' + formatAmount(amount) + '</td>';
                    tbody += '<td>';
                    tbody += '<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDepartmentBillDetail(\'' + transferDepartment + '\', \'' + expenseType + '\', \'' + allocationTable.billingCycle + '\')"><i class="fa fa-eye"></i>在线查看明细</a> ';
                    tbody += '<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="exportDepartmentBillDetail(\'' + transferDepartment + '\', \'' + expenseType + '\', \'' + allocationTable.billingCycle + '\')"><i class="fa fa-download"></i>导出明细</a>';
                    tbody += '</td>';
                    tbody += '</tr>';
                });
                
                // 添加合计行
                tbody += '<tr class="table-info">';
                tbody += '<td colspan="4" class="text-center"><strong>合计</strong></td>';
                tbody += '<td><strong>' + formatAmount(totalAmount) + '</strong></td>';
                tbody += '<td></td>'; // 操作列留空
                tbody += '</tr>';
                
                $("#allocationTableBody").html(tbody);
            });
        }

        function formatAmount(amount) {
            if (!amount || amount == 0) {
                return '0.00';
            }
            return parseFloat(amount).toFixed(2);
        }

        /**
         * 在线查看部门账单明细
         */
        function viewDepartmentBillDetail(transferDepartment, expenseType, billingCycle) {
            $.modal.openOptions({
                title: '账单明细 - ' + transferDepartment,
                url: ctx + '/expense/bill_verify/departmentDetail?transferDepartment=' + encodeURIComponent(transferDepartment) +
                     '&expenseType=' + encodeURIComponent(expenseType) +
                     '&billingCycle=' + encodeURIComponent(billingCycle),
                width: 1200,
                height: 600,
                callback: function(index, layero) {
                    // 可以在这里添加回调处理
                }
            });
        }

        /**
         * 导出部门账单明细
         */
        function exportDepartmentBillDetail(transferDepartment, expenseType, billingCycle) {
            $.modal.confirm("是否导出该部门的费用明细？", function() {
                var url = ctx + "/expense/bill_verify/exportDepartmentDetail?transferDepartment=" + encodeURIComponent(transferDepartment) +
                         "&expenseType=" + encodeURIComponent(expenseType) +
                         "&billingCycle=" + encodeURIComponent(billingCycle);
                window.open(url, "_blank");
            });
        }
    </script>
</body>
</html> 